import { MigrationInterface, QueryRunner } from "typeorm";

export class AddParticipantCourseMappingToLearnerPlan1750500000000 implements MigrationInterface {
    name = 'AddParticipantCourseMappingToLearnerPlan1750500000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if participant_course_mapping column exists
        const hasParticipantCourseMappingColumn = await queryRunner.hasColumn("learner_plan", "participant_course_mapping");
        
        if (!hasParticipantCourseMappingColumn) {
            await queryRunner.query(`
                ALTER TABLE "learner_plan" 
                ADD "participant_course_mapping" json
            `);
            console.log('✅ Added participant_course_mapping column to learner_plan table');
        } else {
            console.log('ℹ️ participant_course_mapping column already exists in learner_plan table');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove participant_course_mapping column
        const hasParticipantCourseMappingColumn = await queryRunner.hasColumn("learner_plan", "participant_course_mapping");
        
        if (hasParticipantCourseMappingColumn) {
            await queryRunner.query(`ALTER TABLE "learner_plan" DROP COLUMN "participant_course_mapping"`);
            console.log('✅ Removed participant_course_mapping column from learner_plan table');
        }
    }
}
